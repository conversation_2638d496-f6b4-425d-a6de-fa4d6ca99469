import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { CacheConfigService } from './cache-config.service';
import { CacheStats } from './interfaces/cache-stats.interface';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private cacheHits = 0;
  private cacheMisses = 0;
  private cacheErrors = 0;

  private redisAvailable = true;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: CacheConfigService,
  ) {
    // Check if Redis is available
    this.checkRedisConnection();

    // Set up periodic connection check every 30 seconds
    setInterval(() => this.checkRedisConnection(), 30000);
  }

  /**
   * Checks if Redis connection is available
   */
  private async checkRedisConnection(): Promise<void> {
    try {
      const wasAvailable = this.redisAvailable;
      this.redisAvailable = await this.redisService.isHealthy();

      // Check if Redis is in read-only mode (only for cluster/replica configurations)
      const redisMode = process.env.REDIS_MODE || 'single';
      const redisProvider = process.env.REDIS_PROVIDER || 'redis';

      // Only check replication status for cluster mode or ElastiCache
      if (redisMode === 'cluster' || redisProvider === 'elasticache') {
        try {
          const info = await this.redisService.client.info('replication');
          const isReplica =
            info.includes('role:slave') || info.includes('role:replica');

          if (isReplica) {
            this.logger.warn(
              'Cache service connected to a Redis read-only replica. ' +
                'Write operations will be skipped, but read operations will continue to work.',
            );

            // We can still read from a replica, so keep redisAvailable true
            // but we'll need to handle write operations specially
          }
        } catch (infoError) {
          // Only log replication check failures for cluster/replica setups
          this.logger.debug(
            'Failed to check Redis replication status in cluster/replica mode',
            infoError,
          );
        }
      }

      if (!wasAvailable && this.redisAvailable) {
        this.logger.log('Redis connection restored, cache is now available');
      } else if (wasAvailable && !this.redisAvailable) {
        this.logger.warn(
          'Redis connection lost, cache is temporarily unavailable',
        );
      }
    } catch (error) {
      this.redisAvailable = false;
      this.logger.error('Failed to check Redis connection', error);
    }
  }

  /**
   * Generates a cache key with proper versioning and namespacing
   * @param key - Base key or key parts
   * @param prefix - Optional namespace prefix
   * @returns Formatted cache key
   */
  generateKey(key: string | string[], prefix?: string): string {
    const baseKey = Array.isArray(key) ? key.join(':') : key;
    const version = this.configService.getCacheVersion();
    const namespace = this.configService.getNamespace();

    const parts = [namespace];
    if (prefix) parts.push(prefix);
    parts.push(`v${version}`);
    parts.push(baseKey);

    return parts.join(':');
  }

  /**
   * Generates a resource-specific cache key
   * @param id - Resource ID
   * @param prefix - Resource type prefix
   * @returns Formatted cache key
   */
  generateResourceKey(id: string, prefix: string): string {
    return this.generateKey(id, prefix);
  }

  /**
   * Retrieves a value from cache
   * @param key - Cache key
   * @returns Cached value or null if not found
   */
  async get<T = any>(key: string): Promise<T | null> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      this.cacheMisses++;
      return null;
    }

    try {
      const value = await this.redisService.get(key);
      if (value !== null) {
        this.cacheHits++;
        this.logger.debug(`Cache hit for key: ${key}`);
        return value as T;
      }
      this.cacheMisses++;
      this.logger.debug(`Cache miss for key: ${key}`);
      return null;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        `Failed to get cache for key ${key}`,
        error instanceof Error ? error.message : String(error),
      );
      return null;
    }
  }

  /**
   * Stores a value in cache
   * @param key - Cache key
   * @param value - Value to store
   * @param ttl - Optional TTL in seconds
   * @returns Success status
   */
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      return false;
    }

    try {
      await this.redisService.set(key, value, ttl);
      this.logger.debug(
        `Cache set for key: ${key}${ttl ? ` with TTL: ${ttl}s` : ''}`,
      );
      return true;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        `Failed to set cache for key ${key}`,
        error instanceof Error ? error.message : String(error),
      );
      return false;
    }
  }

  /**
   * Deletes a value from cache
   * @param key - Cache key
   * @returns Success status
   */
  async del(key: string): Promise<boolean> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      return false;
    }

    try {
      await this.redisService.del(key);
      this.logger.debug(`Cache deleted for key: ${key}`);
      return true;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        `Failed to delete cache for key ${key}`,
        error instanceof Error ? error.message : String(error),
      );
      return false;
    }
  }

  /**
   * Invalidates multiple cache keys
   * @param keys - Array of keys to invalidate
   * @param prefix - Optional prefix to apply to all keys
   */
  async invalidateMany(keys: string[], prefix?: string): Promise<void> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      return;
    }

    try {
      const fullKeys = keys.map((key) => this.generateKey(key, prefix));
      await Promise.all(fullKeys.map((key) => this.del(key)));
      this.logger.debug(
        `Invalidated ${keys.length} cache keys with prefix: ${prefix ?? 'none'}`,
      );
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        'Failed to invalidate multiple keys',
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * Invalidates all keys matching a pattern
   * @param pattern - Redis key pattern (e.g., "user:*")
   */
  async invalidatePattern(pattern: string): Promise<number> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      return 0;
    }

    try {
      const keys = await this.redisService.client.keys(pattern);
      if (keys.length > 0) {
        await this.redisService.client.del(...keys);
      }
      this.logger.debug(
        `Invalidated ${keys.length} cache keys matching pattern: ${pattern}`,
      );
      return keys.length;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        `Failed to invalidate pattern ${pattern}`,
        error instanceof Error ? error.message : String(error),
      );
      return 0;
    }
  }

  /**
   * Gets cache statistics
   */
  getStats(): CacheStats {
    const total = this.cacheHits + this.cacheMisses;
    const hitRate = total > 0 ? (this.cacheHits / total) * 100 : 0;

    return {
      hits: this.cacheHits,
      misses: this.cacheMisses,
      errors: this.cacheErrors,
      hitRate: Math.round(hitRate * 100) / 100, // Round to 2 decimal places
      total,
    };
  }

  /**
   * Resets cache statistics
   */
  resetStats(): void {
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.cacheErrors = 0;
    this.logger.debug('Cache statistics reset');
  }

  /**
   * Clears all cache entries
   */
  async clearAll(): Promise<boolean> {
    // Skip cache if Redis is not available
    if (!this.redisAvailable) {
      return false;
    }

    try {
      await this.redisService.clearAll();
      this.logger.debug('All cache entries cleared');
      return true;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        'Failed to clear all cache entries',
        error instanceof Error ? error.message : String(error),
      );
      return false;
    }
  }

  /**
   * Increments the cache version for a specific prefix
   * This effectively invalidates all caches for that prefix
   * @param prefix - The cache prefix to increment version for
   * @returns Success status
   */
  async incrementCacheVersion(prefix: string): Promise<boolean> {
    if (!this.redisAvailable) {
      return false;
    }

    try {
      // Store the version increment in Redis
      const versionKey = `${this.configService.getNamespace()}:${prefix}:version`;
      const currentVersion = (await this.redisService.get(versionKey)) || '0';
      const newVersion = (
        parseInt(currentVersion as string, 10) + 1
      ).toString();

      await this.redisService.set(versionKey, newVersion);

      this.logger.debug(
        `Incremented cache version for prefix ${prefix} to ${newVersion}`,
      );
      return true;
    } catch (error) {
      this.cacheErrors++;
      this.logger.error(
        `Failed to increment cache version for prefix ${prefix}`,
        error instanceof Error ? error.message : String(error),
      );
      return false;
    }
  }
}
