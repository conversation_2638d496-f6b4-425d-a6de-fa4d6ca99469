
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { CacheService } from './cache.service';
import { RedisService } from '../redis/redis.service';
import { CACHE_PREFIXES, CACHE_TTL } from '../constants/cache.constant';
import { SchedulerRegistry } from '@nestjs/schedule';

@Injectable()
export class CacheManagerService implements OnModuleInit {
  private readonly logger = new Logger(CacheManagerService.name);
  private readonly CACHE_CLEANUP_INTERVAL = 3600000; // 1 hour in milliseconds
  private readonly isProduction = process.env.NODE_ENV === 'production';

  private redisAvailable = true;
  private hasLoggedUnavailable = false;

  constructor(
    private readonly cacheService: CacheService,
    private readonly redisService: RedisService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {
    // Check Redis availability asynchronously
    this.checkRedisConnection().catch((error) => {
      this.logger.error(
        'Failed to check Redis connection during initialization',
        error instanceof Error ? error.message : String(error),
      );
    });
  }

  /**
   * Checks if Redis connection is available with throttling to reduce noise
   */
  private async checkRedisConnection(): Promise<void> {
    try {
      this.redisAvailable = await this.redisService.isHealthy();

      // Only log warnings once when Redis becomes unavailable
      if (!this.redisAvailable && !this.hasLoggedUnavailable) {
        this.logger.warn(
          'Redis connection unavailable, cache management operations will be skipped',
        );
        this.hasLoggedUnavailable = true;
      } else if (this.redisAvailable && this.hasLoggedUnavailable) {
        // Log when Redis becomes available again
        this.logger.log(
          'Redis connection restored, cache management operations resumed',
        );
        this.hasLoggedUnavailable = false;
      }
    } catch (error) {
      this.redisAvailable = false;

      // Only log error once to reduce noise
      if (!this.hasLoggedUnavailable) {
        this.logger.error(
          'Failed to check Redis connection',
          error instanceof Error ? error.message : String(error),
        );
        this.hasLoggedUnavailable = true;
      }
    }
  }

  async onModuleInit() {
    try {
      // Check if interval already exists
      const intervals = this.schedulerRegistry.getIntervals();
      const hasInterval = intervals.includes('cache-cleanup');

      if (!hasInterval) {
        // Schedule periodic cache cleanup
        const interval = setInterval(() => {
          this.cleanupStaleCache().catch((error) => {
            this.logger.error(
              'Cache cleanup failed during scheduled execution:',
              error instanceof Error ? error.message : String(error),
            );
          });
        }, this.CACHE_CLEANUP_INTERVAL);

        this.schedulerRegistry.addInterval('cache-cleanup', interval);
        this.logger.log('Cache cleanup scheduled');
      } else {
        this.logger.debug(
          'Cache cleanup interval already exists, skipping registration',
        );
      }
    } catch (error) {
      this.logger.warn(
        'Failed to schedule cache cleanup:',
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * Cleans up stale cache entries
   */
  async cleanupStaleCache() {
    this.logCleanupStart();

    if (!(await this.ensureRedisAvailable())) {
      return;
    }

    try {
      const cleanedKeys = await this.processStaleKeys();
      this.logCleanupResults(cleanedKeys);
    } catch (error) {
      this.handleCleanupError(error);
    }
  }

  /**
   * Logs cleanup start message in development
   */
  private logCleanupStart(): void {
    if (!this.isProduction) {
      this.logger.debug('Running cache cleanup');
    }
  }

  /**
   * Ensures Redis is available for cleanup operations
   */
  private async ensureRedisAvailable(): Promise<boolean> {
    await this.checkRedisConnection();
    if (!this.redisAvailable) {
      if (!this.isProduction) {
        this.logger.warn('Skipping cache cleanup due to Redis unavailability');
      }
      return false;
    }
    return true;
  }

  /**
   * Processes stale keys and applies TTL to known prefixes
   */
  private async processStaleKeys(): Promise<number> {
    const keys = await this.redisService.client.keys('*');
    let cleanedKeys = 0;

    for (const key of keys) {
      if (await this.shouldApplyTtlToKey(key)) {
        await this.redisService.client.expire(key, CACHE_TTL.ONE_DAY);
        cleanedKeys++;
      }
    }

    return cleanedKeys;
  }

  /**
   * Determines if TTL should be applied to a key
   */
  private async shouldApplyTtlToKey(key: string): Promise<boolean> {
    const ttl = await this.redisService.client.ttl(key);

    // TTL = -1 means no expiration set
    if (ttl !== -1) {
      return false;
    }

    // Check if key matches any known prefix
    return Object.values(CACHE_PREFIXES).some((prefix) =>
      key.includes(`:${prefix}:`),
    );
  }

  /**
   * Logs cleanup results based on environment and significance
   */
  private logCleanupResults(cleanedKeys: number): void {
    if (!this.isProduction || cleanedKeys > 10) {
      this.logger.debug(
        `Cache cleanup complete: ${cleanedKeys} keys updated with TTL`,
      );
    }
  }

  /**
   * Handles cleanup errors with appropriate logging
   */
  private handleCleanupError(error: unknown): void {
    this.redisAvailable = false;
    if (!this.isProduction) {
      this.logger.error(
        'Cache cleanup failed:',
        error instanceof Error ? error.message : String(error),
      );
    }
  }

  /**
   * Gets cache memory usage statistics
   */
  async getMemoryStats() {
    // Check Redis availability before proceeding
    await this.checkRedisConnection();
    if (!this.redisAvailable) {
      this.logger.warn(
        'Skipping memory stats retrieval due to Redis unavailability',
      );
      return { error: 'Redis unavailable' };
    }

    try {
      const info = await this.redisService.client.info('memory');
      const stats: Record<string, string> = {};

      // Parse Redis INFO output
      info.split('\r\n').forEach((line) => {
        if (line.includes(':')) {
          const parts = line.split(':');
          if (parts.length >= 2 && parts[0]) {
            const key = parts[0];
            const value = parts[1] ?? '';
            stats[key] = value;
          }
        }
      });

      return stats;
    } catch (error) {
      this.redisAvailable = false;
      this.logger.error(
        'Failed to get memory stats:',
        error instanceof Error ? error.message : String(error),
      );
      return { error: 'Failed to retrieve memory stats' };
    }
  }

  /**
   * Gets cache key statistics by prefix
   */
  async getKeyStatsByPrefix() {
    // Check Redis availability before proceeding
    await this.checkRedisConnection();
    if (!this.redisAvailable) {
      this.logger.warn(
        'Skipping key stats retrieval due to Redis unavailability',
      );
      return { error: 'Redis unavailable' };
    }

    try {
      const stats: Record<string, number> = {};

      // Count keys for each prefix
      for (const prefix of Object.values(CACHE_PREFIXES)) {
        const keys = await this.redisService.client.keys(`*:${prefix}:*`);
        stats[prefix] = keys.length;
      }

      return stats;
    } catch (error) {
      this.redisAvailable = false;
      this.logger.error(
        'Failed to get key stats:',
        error instanceof Error ? error.message : String(error),
      );
      return { error: 'Failed to retrieve key stats' };
    }
  }

  /**
   * Clears cache for a specific prefix
   */
  async clearByPrefix(prefix: string) {
    // Check Redis availability before proceeding
    await this.checkRedisConnection();
    if (!this.redisAvailable) {
      this.logger.warn(
        `Skipping cache clearing for prefix '${prefix}' due to Redis unavailability`,
      );
      return 0;
    }

    try {
      const pattern = `*:${prefix}:*`;
      const keys = await this.redisService.client.keys(pattern);

      if (keys.length > 0) {
        await this.redisService.client.del(...keys);
      }

      // Only log significant cache clearing operations
      if (!this.isProduction || keys.length > 5) {
        this.logger.debug(
          `Cleared ${keys.length} cache entries with prefix '${prefix}'`,
        );
      }
      return keys.length;
    } catch (error) {
      this.redisAvailable = false;
      // Only log errors in development
      if (!this.isProduction) {
        this.logger.error(
          `Failed to clear cache by prefix ${prefix}:`,
          error instanceof Error ? error.message : String(error),
        );
      }
      return 0;
    }
  }
}
