import { Test, TestingModule } from '@nestjs/testing';
import { LeaderboardProcessor } from './leaderboard.processor';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { Job } from 'bullmq';

describe('LeaderboardProcessor', () => {
  let processor: LeaderboardProcessor;
  let drizzleService: jest.Mocked<DrizzleService>;
  let redisService: jest.Mocked<RedisService>;

  const mockDrizzleService = {
    db: {
      execute: jest.fn(),
    },
  };

  const mockRedisClient = {
    ttl: jest.fn(),
    pipeline: jest.fn(),
    zincrby: jest.fn(),
    expire: jest.fn(),
    del: jest.fn(),
    zadd: jest.fn(),
    exec: jest.fn(),
  };

  const mockRedisService = {
    client: mockRedisClient,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeaderboardProcessor,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    processor = module.get<LeaderboardProcessor>(LeaderboardProcessor);
    drizzleService = module.get<DrizzleService>(DrizzleService) as jest.Mocked<DrizzleService>;
    redisService = module.get<RedisService>(RedisService) as jest.Mocked<RedisService>;
    
    // Setup pipeline mock
    const mockPipeline = {
      zincrby: jest.fn().mockReturnThis(),
      expire: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    };
    mockRedisClient.pipeline.mockReturnValue(mockPipeline);
    
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process refresh-materialized-views job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      // Mock database execution for materialized view refresh
      mockDrizzleService.db.execute
        .mockResolvedValueOnce({ rows: [] }) // weekly refresh
        .mockResolvedValueOnce({ rows: [] }) // monthly refresh
        .mockResolvedValueOnce({ rows: [] }) // all-time refresh
        .mockResolvedValueOnce({ 
          rows: [
            { student_id: 'student-1', total_score: 100 },
            { student_id: 'student-2', total_score: 90 },
          ] 
        }) // weekly data
        .mockResolvedValueOnce({ 
          rows: [
            { student_id: 'student-1', total_score: 200 },
            { student_id: 'student-2', total_score: 180 },
          ] 
        }) // monthly data
        .mockResolvedValueOnce({ 
          rows: [
            { student_id: 'student-1', total_score: 500 },
            { student_id: 'student-2', total_score: 450 },
          ] 
        }); // all-time data

      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.zadd.mockResolvedValue(2);
      mockRedisClient.expire.mockResolvedValue(1);

      await processor.process(job);

      expect(mockDrizzleService.db.execute).toHaveBeenCalledTimes(6);
      expect(mockRedisClient.del).toHaveBeenCalledTimes(3);
      expect(mockRedisClient.zadd).toHaveBeenCalledTimes(3);
      expect(mockRedisClient.expire).toHaveBeenCalledTimes(3);
    });

    it('should process update-realtime-leaderboard job successfully', async () => {
      const job = {
        id: 'test-job-2',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 50,
          period: 'weekly',
        },
      } as Job;

      mockRedisClient.ttl.mockResolvedValue(3600); // 1 hour remaining
      
      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockRedisClient.ttl).toHaveBeenCalled();
      expect(mockRedisClient.pipeline).toHaveBeenCalled();
      expect(mockPipeline.zincrby).toHaveBeenCalledWith(
        expect.stringContaining('leaderboard:weekly'),
        50,
        'student-123'
      );
    });

    it('should handle update-realtime-leaderboard with TTL refresh needed', async () => {
      const job = {
        id: 'test-job-3',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 25,
          period: 'monthly',
        },
      } as Job;

      mockRedisClient.ttl.mockResolvedValue(-1); // No expiration set
      
      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockPipeline.expire).toHaveBeenCalled();
    });

    it('should handle update-realtime-leaderboard with key not existing', async () => {
      const job = {
        id: 'test-job-4',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 75,
          period: 'all-time',
        },
      } as Job;

      mockRedisClient.ttl.mockResolvedValue(-2); // Key doesn't exist
      
      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockPipeline.expire).toHaveBeenCalled();
    });

    it('should handle refresh-materialized-views with database error', async () => {
      const job = {
        id: 'test-job-5',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      const error = new Error('Database connection failed');
      mockDrizzleService.db.execute.mockRejectedValue(error);

      await expect(processor.process(job)).rejects.toThrow('Database connection failed');
    });

    it('should handle update-realtime-leaderboard with Redis error', async () => {
      const job = {
        id: 'test-job-6',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 30,
          period: 'weekly',
        },
      } as Job;

      const error = new Error('Redis connection failed');
      mockRedisClient.ttl.mockRejectedValue(error);

      await expect(processor.process(job)).rejects.toThrow('Redis connection failed');
    });

    it('should warn for unknown job type', async () => {
      const job = {
        id: 'test-job-7',
        name: 'unknown-job-type',
        data: {},
      } as Job;

      const warnSpy = jest.spyOn(processor['logger'], 'warn').mockImplementation();

      await processor.process(job);

      expect(warnSpy).toHaveBeenCalledWith('Unknown job type: unknown-job-type');
      warnSpy.mockRestore();
    });

    it('should handle materialized view refresh with empty results', async () => {
      const job = {
        id: 'test-job-8',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      // Mock empty results for all queries
      mockDrizzleService.db.execute.mockResolvedValue({ rows: [] });

      await processor.process(job);

      expect(mockDrizzleService.db.execute).toHaveBeenCalledTimes(6);
      // Should not call zadd when no data
      expect(mockRedisClient.zadd).not.toHaveBeenCalled();
    });

    it('should handle pipeline execution errors gracefully', async () => {
      const job = {
        id: 'test-job-9',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 40,
          period: 'weekly',
        },
      } as Job;

      mockRedisClient.ttl.mockResolvedValue(3600);
      
      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(new Error('Pipeline execution failed')),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await expect(processor.process(job)).rejects.toThrow('Pipeline execution failed');
    });

    it('should handle different period types correctly', async () => {
      const periods = ['weekly', 'monthly', 'all-time'];
      
      for (const period of periods) {
        const job = {
          id: `test-job-${period}`,
          name: 'update-realtime-leaderboard',
          data: {
            student_id: 'student-123',
            points: 10,
            period,
          },
        } as Job;

        mockRedisClient.ttl.mockResolvedValue(3600);
        
        const mockPipeline = {
          zincrby: jest.fn().mockReturnThis(),
          expire: jest.fn().mockReturnThis(),
          exec: jest.fn().mockResolvedValue([]),
        };
        mockRedisClient.pipeline.mockReturnValue(mockPipeline);

        await processor.process(job);

        expect(mockPipeline.zincrby).toHaveBeenCalledWith(
          expect.stringContaining(`leaderboard:${period}`),
          10,
          'student-123'
        );
      }
    });
  });
});
