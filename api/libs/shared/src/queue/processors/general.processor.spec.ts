import { Test, TestingModule } from '@nestjs/testing';
import { GeneralProcessor } from './general.processor';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '@/mail/email.service';

describe('GeneralProcessor - Retry Mechanism', () => {
  let processor: GeneralProcessor;
  let drizzleService: jest.Mocked<DrizzleService>;

  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';
  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    role: 'student',
    student_profile: null,
    profile: null,
  };

  beforeEach(async () => {
    const mockDrizzleService = {
      db: {
        query: {
          users: {
            findFirst: jest.fn().mockResolvedValue(mockUser),
          },
        },
        execute: jest.fn().mockResolvedValue({
          rows: [
            {
              constraint_name: 'questions_created_by_users_id_fk',
              constraint_type: 'FOREIGN KEY',
              delete_rule: 'SET NULL',
              update_rule: 'NO ACTION',
            },
          ],
        }),
        transaction: jest.fn(),
      },
    };

    const mockEmailService = {
      sendCustomEmail: jest.fn().mockResolvedValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeneralProcessor,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    processor = module.get<GeneralProcessor>(GeneralProcessor);
    drizzleService = module.get(DrizzleService);
  });

  describe('isQueryTimeoutError', () => {
    it('should detect query read timeout errors', () => {
      const timeoutError = new Error('Query read timeout');
      expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
    });

    it('should detect general query timeout errors', () => {
      const timeoutError = new Error('Database query timeout occurred');
      expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
    });

    it('should not detect non-timeout errors', () => {
      const regularError = new Error('User not found');
      expect(processor['isQueryTimeoutError'](regularError)).toBe(false);
    });

    it('should handle case insensitive timeout detection', () => {
      const timeoutError = new Error('QUERY READ TIMEOUT');
      expect(processor['isQueryTimeoutError'](timeoutError)).toBe(true);
    });

    it('should handle errors without message', () => {
      const errorWithoutMessage = new Error();
      expect(processor['isQueryTimeoutError'](errorWithoutMessage)).toBe(false);
    });
  });

  describe('deleteUserByIdWithRetry', () => {
    beforeEach(() => {
      // Mock successful transaction by default
      drizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          return await callback({
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([]), // Empty arrays for cleanup stats
              }),
            }),
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: mockUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
          });
        });
    });

    it('should succeed on first attempt when no timeout occurs', async () => {
      const result = await processor['deleteUserByIdWithRetry'](mockUserId);

      expect(result).toBeDefined();
      expect(result?.id).toBe(mockUserId);
      expect(result?.email).toBe('<EMAIL>');
    });

    it('should retry once on query timeout and succeed', async () => {
      let callCount = 0;
      const originalTransaction = drizzleService.db.transaction;

      drizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          callCount++;
          if (callCount === 1) {
            throw new Error('Query read timeout');
          }
          return originalTransaction(callback);
        });

      const result = await processor['deleteUserByIdWithRetry'](mockUserId);

      expect(result).toBeDefined();
      expect(result?.id).toBe(mockUserId);
      expect(callCount).toBe(2); // First attempt failed, second succeeded
    });

    it('should fail after retry when timeout persists', async () => {
      drizzleService.db.transaction = jest
        .fn()
        .mockRejectedValue(new Error('Query read timeout'));

      await expect(
        processor['deleteUserByIdWithRetry'](mockUserId),
      ).rejects.toThrow('Query read timeout');
    });

    it('should not retry non-timeout errors', async () => {
      let callCount = 0;
      drizzleService.db.transaction = jest.fn().mockImplementation(async () => {
        callCount++;
        throw new Error('User not found');
      });

      await expect(
        processor['deleteUserByIdWithRetry'](mockUserId),
      ).rejects.toThrow('User not found');
      expect(callCount).toBe(1); // Should not retry
    });

    it('should wait 1 second before retry', async () => {
      const startTime = Date.now();
      let callCount = 0;

      drizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          callCount++;
          if (callCount === 1) {
            throw new Error('Query read timeout');
          }
          return await callback({
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([]),
              }),
            }),
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: mockUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
          });
        });

      await processor['deleteUserByIdWithRetry'](mockUserId);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should take at least 1000ms due to the retry delay
      expect(duration).toBeGreaterThanOrEqual(1000);
      expect(callCount).toBe(2);
    });
  });
});
